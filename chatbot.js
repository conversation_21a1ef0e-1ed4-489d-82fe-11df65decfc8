class ChatBot {
  constructor() {
    this.name = "ChatBot";
    this.responses = this.initializeResponses();
    this.conversationHistory = new Map();
    this.userProfiles = new Map();
  }

  initializeResponses() {
    return {
      greetings: [
        "Hello! How can I help you today?",
        "Hi there! What's on your mind?",
        "Hey! Great to see you here!",
        "Greetings! How are you doing?",
        "Hello! I'm here to chat with you!"
      ],
      
      farewells: [
        "Goodbye! Have a great day!",
        "See you later! Take care!",
        "Bye! It was nice chatting with you!",
        "<PERSON><PERSON>ell! Come back anytime!",
        "Take care! See you soon!"
      ],
      
      thanks: [
        "You're welcome! Happy to help!",
        "No problem at all!",
        "Glad I could assist you!",
        "Anytime! That's what I'm here for!",
        "You're very welcome!"
      ],
      
      questions: {
        name: [
          "I'm <PERSON><PERSON><PERSON><PERSON>, your friendly AI assistant!",
          "You can call me <PERSON><PERSON><PERSON><PERSON>! What's your name?",
          "I'm <PERSON><PERSON><PERSON><PERSON>, nice to meet you!"
        ],
        
        time: () => {
          const now = new Date();
          return `It's currently ${now.toLocaleTimeString()} on ${now.toLocaleDateString()}`;
        },
        
        weather: [
          "I don't have access to real-time weather data, but you can check your local weather app!",
          "I wish I could tell you about the weather, but I don't have that information right now.",
          "For weather updates, I'd recommend checking a weather website or app!"
        ],
        
        help: [
          "I can chat with you about various topics! Try asking me about myself, the time, or just have a casual conversation.",
          "I'm here to chat! You can ask me questions, tell me about your day, or just talk about anything on your mind.",
          "I can help with casual conversation, answer basic questions, or just be here to listen!"
        ]
      },
      
      emotions: {
        happy: [
          "That's wonderful! I'm glad you're feeling good!",
          "Awesome! Your positive energy is contagious!",
          "That's great to hear! What's making you happy today?"
        ],
        
        sad: [
          "I'm sorry you're feeling down. Would you like to talk about it?",
          "That sounds tough. I'm here if you need someone to listen.",
          "I understand. Sometimes it helps to share what's bothering you."
        ],
        
        excited: [
          "That's exciting! Tell me more about it!",
          "Wow! Your enthusiasm is amazing!",
          "That sounds incredible! I'd love to hear the details!"
        ]
      },
      
      default: [
        "That's interesting! Tell me more about that.",
        "I see! What do you think about that?",
        "Hmm, that's a good point. Can you elaborate?",
        "That's fascinating! I'd love to learn more.",
        "Interesting perspective! What made you think of that?",
        "I'm not sure I fully understand, but I'm listening!",
        "That's something to think about. What's your take on it?",
        "I appreciate you sharing that with me!"
      ]
    };
  }

  async generateResponse(message, username) {
    const lowerMessage = message.toLowerCase().trim();
    
    // Update conversation history
    this.updateConversationHistory(username, message);
    
    // Check for specific patterns
    if (this.isGreeting(lowerMessage)) {
      return this.getRandomResponse(this.responses.greetings);
    }
    
    if (this.isFarewell(lowerMessage)) {
      return this.getRandomResponse(this.responses.farewells);
    }
    
    if (this.isThanking(lowerMessage)) {
      return this.getRandomResponse(this.responses.thanks);
    }
    
    // Handle questions
    if (this.isAskingName(lowerMessage)) {
      return this.getRandomResponse(this.responses.questions.name);
    }
    
    if (this.isAskingTime(lowerMessage)) {
      return this.responses.questions.time();
    }
    
    if (this.isAskingWeather(lowerMessage)) {
      return this.getRandomResponse(this.responses.questions.weather);
    }
    
    if (this.isAskingHelp(lowerMessage)) {
      return this.getRandomResponse(this.responses.questions.help);
    }
    
    // Handle emotions
    if (this.isExpressingEmotion(lowerMessage, 'happy')) {
      return this.getRandomResponse(this.responses.emotions.happy);
    }
    
    if (this.isExpressingEmotion(lowerMessage, 'sad')) {
      return this.getRandomResponse(this.responses.emotions.sad);
    }
    
    if (this.isExpressingEmotion(lowerMessage, 'excited')) {
      return this.getRandomResponse(this.responses.emotions.excited);
    }
    
    // Handle specific topics
    if (lowerMessage.includes('joke')) {
      return this.getJoke();
    }
    
    if (lowerMessage.includes('fact')) {
      return this.getFunFact();
    }
    
    if (lowerMessage.includes('quote')) {
      return this.getInspirationalQuote();
    }
    
    // Personalized responses based on conversation history
    const personalizedResponse = this.getPersonalizedResponse(username, lowerMessage);
    if (personalizedResponse) {
      return personalizedResponse;
    }
    
    // Default response
    return this.getRandomResponse(this.responses.default);
  }

  updateConversationHistory(username, message) {
    if (!this.conversationHistory.has(username)) {
      this.conversationHistory.set(username, []);
    }
    
    const history = this.conversationHistory.get(username);
    history.push({
      message: message,
      timestamp: new Date()
    });
    
    // Keep only last 10 messages
    if (history.length > 10) {
      history.shift();
    }
  }

  getPersonalizedResponse(username, message) {
    const history = this.conversationHistory.get(username);
    if (!history || history.length < 2) return null;
    
    // Check if user is repeating themselves
    const recentMessages = history.slice(-3).map(h => h.message.toLowerCase());
    if (recentMessages.filter(m => m === message).length > 1) {
      return "I notice you mentioned that before. Is there something specific you'd like to explore about it?";
    }
    
    return null;
  }

  isGreeting(message) {
    const greetings = ['hello', 'hi', 'hey', 'greetings', 'good morning', 'good afternoon', 'good evening'];
    return greetings.some(greeting => message.includes(greeting));
  }

  isFarewell(message) {
    const farewells = ['bye', 'goodbye', 'see you', 'farewell', 'take care', 'later'];
    return farewells.some(farewell => message.includes(farewell));
  }

  isThanking(message) {
    const thanks = ['thank', 'thanks', 'appreciate', 'grateful'];
    return thanks.some(thank => message.includes(thank));
  }

  isAskingName(message) {
    return message.includes('your name') || message.includes('who are you') || message.includes('what are you');
  }

  isAskingTime(message) {
    return message.includes('time') || message.includes('date') || message.includes('what time');
  }

  isAskingWeather(message) {
    return message.includes('weather') || message.includes('temperature') || message.includes('rain');
  }

  isAskingHelp(message) {
    return message.includes('help') || message.includes('what can you do') || message.includes('how can you');
  }

  isExpressingEmotion(message, emotion) {
    const emotionWords = {
      happy: ['happy', 'glad', 'joy', 'excited', 'great', 'awesome', 'wonderful', 'fantastic'],
      sad: ['sad', 'down', 'depressed', 'upset', 'unhappy', 'disappointed', 'blue'],
      excited: ['excited', 'thrilled', 'pumped', 'enthusiastic', 'eager', 'can\'t wait']
    };
    
    return emotionWords[emotion]?.some(word => message.includes(word)) || false;
  }

  getJoke() {
    const jokes = [
      "Why don't scientists trust atoms? Because they make up everything!",
      "Why did the scarecrow win an award? He was outstanding in his field!",
      "Why don't eggs tell jokes? They'd crack each other up!",
      "What do you call a fake noodle? An impasta!",
      "Why did the math book look so sad? Because it had too many problems!"
    ];
    return this.getRandomResponse(jokes);
  }

  getFunFact() {
    const facts = [
      "Did you know? Octopuses have three hearts and blue blood!",
      "Fun fact: A group of flamingos is called a 'flamboyance'!",
      "Here's a fact: Honey never spoils. Archaeologists have found edible honey in ancient Egyptian tombs!",
      "Did you know? A single cloud can weigh more than a million pounds!",
      "Fun fact: Bananas are berries, but strawberries aren't!"
    ];
    return this.getRandomResponse(facts);
  }

  getInspirationalQuote() {
    const quotes = [
      "\"The only way to do great work is to love what you do.\" - Steve Jobs",
      "\"Innovation distinguishes between a leader and a follower.\" - Steve Jobs",
      "\"Life is what happens to you while you're busy making other plans.\" - John Lennon",
      "\"The future belongs to those who believe in the beauty of their dreams.\" - Eleanor Roosevelt",
      "\"It is during our darkest moments that we must focus to see the light.\" - Aristotle"
    ];
    return this.getRandomResponse(quotes);
  }

  getRandomResponse(responses) {
    return responses[Math.floor(Math.random() * responses.length)];
  }
}

module.exports = ChatBot;
