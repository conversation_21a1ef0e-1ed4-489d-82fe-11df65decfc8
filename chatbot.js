class ChatBot {
  constructor() {
    this.name = "ChatBot";
    this.responses = this.initializeResponses();
    this.conversationHistory = new Map();
    this.userProfiles = new Map();
  }

  initializeResponses() {
    return {
      greetings: [
        "Hello! How can I help you today?",
        "Hi there! What's on your mind?",
        "Hey! Great to see you here!",
        "Greetings! How are you doing?",
        "Hello! I'm here to chat with you!"
      ],
      
      farewells: [
        "Goodbye! Have a great day!",
        "See you later! Take care!",
        "Bye! It was nice chatting with you!",
        "<PERSON><PERSON>ell! Come back anytime!",
        "Take care! See you soon!"
      ],
      
      thanks: [
        "You're welcome! Happy to help!",
        "No problem at all!",
        "Glad I could assist you!",
        "Anytime! That's what I'm here for!",
        "You're very welcome!"
      ],
      
      questions: {
        name: [
          "I'm <PERSON><PERSON><PERSON><PERSON>, your friendly AI assistant!",
          "You can call me <PERSON><PERSON><PERSON><PERSON>! What's your name?",
          "I'm <PERSON><PERSON><PERSON><PERSON>, nice to meet you!"
        ],
        
        time: () => {
          const now = new Date();
          return `It's currently ${now.toLocaleTimeString()} on ${now.toLocaleDateString()}`;
        },
        
        weather: [
          "I don't have access to real-time weather data, but you can check your local weather app!",
          "I wish I could tell you about the weather, but I don't have that information right now.",
          "For weather updates, I'd recommend checking a weather website or app!"
        ],
        
        help: [
          "I can chat with you about various topics! Try asking me about myself, the time, or just have a casual conversation.",
          "I'm here to chat! You can ask me questions, tell me about your day, or just talk about anything on your mind.",
          "I can help with casual conversation, answer basic questions, or just be here to listen!"
        ]
      },
      
      emotions: {
        happy: [
          "That's wonderful! I'm glad you're feeling good!",
          "Awesome! Your positive energy is contagious!",
          "That's great to hear! What's making you happy today?"
        ],
        
        sad: [
          "I'm sorry you're feeling down. Would you like to talk about it?",
          "That sounds tough. I'm here if you need someone to listen.",
          "I understand. Sometimes it helps to share what's bothering you."
        ],
        
        excited: [
          "That's exciting! Tell me more about it!",
          "Wow! Your enthusiasm is amazing!",
          "That sounds incredible! I'd love to hear the details!"
        ]
      },
      
      invitations: {
        birthday: [
          "🎉 **Birthday Party Invitation** 🎂\n\nYou're invited to celebrate [Name]'s Birthday!\n\n📅 Date: [Date]\n⏰ Time: [Time]\n📍 Location: [Address]\n\n🎈 Join us for cake, fun, and great memories!\n\nRSVP: [Contact Info]\n\nCan't wait to see you there! 🥳",

          "🎂 **It's Party Time!** 🎉\n\nCome celebrate another year of awesome with [Name]!\n\nWhen: [Date] at [Time]\nWhere: [Address]\n\n🎁 Gifts optional, your presence is the present!\n🍰 Cake, games, and good vibes guaranteed!\n\nPlease RSVP by [RSVP Date]\nContact: [Phone/Email]\n\nLet's make it unforgettable! ✨"
        ],

        wedding: [
          "💍 **Wedding Invitation** 💒\n\n[Bride's Name] & [Groom's Name]\nrequest the honor of your presence\nat their wedding celebration\n\n📅 Date: [Wedding Date]\n⏰ Ceremony: [Time]\n📍 Venue: [Venue Name]\n[Address]\n\n💕 Reception to follow\n\nRSVP by [RSVP Date]\n[Contact Information]\n\nJoin us as we begin our journey together! 👰🤵",

          "✨ **Save the Date** ✨\n\n[Bride] & [Groom]\nare getting married!\n\n📅 [Date]\n📍 [Location]\n\nFormal invitation to follow\n\nWe can't wait to celebrate with you! 💖"
        ],

        party: [
          "🎊 **You're Invited to a Party!** 🎉\n\nWhat: [Event Type]\nWhen: [Date] at [Time]\nWhere: [Address]\n\n🎵 Music, dancing, and great company!\n🍕 Food and drinks provided\n\nDress code: [Dress Code]\n\nRSVP: [Contact]\n\nLet's party! 🕺💃",

          "🎈 **Party Alert!** 🚨\n\nYou're invited to join the fun!\n\n📅 Date: [Date]\n⏰ Time: [Time]\n📍 Location: [Venue]\n\n🎯 What to expect:\n• Great music 🎵\n• Delicious food 🍽️\n• Amazing people 👥\n• Unforgettable memories 📸\n\nPlease confirm your attendance!\nContact: [Phone/Email]"
        ],

        business: [
          "📧 **Professional Event Invitation** 📋\n\nDear [Name],\n\nYou are cordially invited to attend:\n\n**[Event Name]**\n\nDate: [Date]\nTime: [Time]\nVenue: [Location]\n\nAgenda:\n• [Agenda Item 1]\n• [Agenda Item 2]\n• [Agenda Item 3]\n\nPlease confirm your attendance by [RSVP Date]\n\nContact: [Email/Phone]\n\nWe look forward to your participation.\n\nBest regards,\n[Your Name/Organization]",

          "🏢 **Corporate Event Invitation** 💼\n\nSubject: Invitation to [Event Name]\n\nDear [Recipient],\n\nWe are pleased to invite you to our upcoming [Event Type].\n\nEvent Details:\n📅 Date: [Date]\n⏰ Time: [Start Time] - [End Time]\n📍 Location: [Venue]\n\nThis event will feature:\n• [Feature 1]\n• [Feature 2]\n• [Feature 3]\n\nDress Code: [Business Attire/Casual/etc.]\n\nKindly RSVP by [Date] to [Contact Information]\n\nThank you for your time.\n\nSincerely,\n[Name]\n[Title]\n[Company]"
        ],

        casual: [
          "😊 **Casual Get-Together** 🏠\n\nHey [Name]!\n\nWant to hang out?\n\nWhen: [Date] at [Time]\nWhere: [Location]\nWhat: [Activity - movie night, BBQ, game night, etc.]\n\n🍿 Snacks and good times included!\n\nLet me know if you can make it!\n\nText me: [Phone]\n\nHope to see you there! 😄",

          "🎮 **Hangout Invitation** 🍕\n\nHey there!\n\nUp for some fun? Join us for:\n\n[Activity/Event]\n📅 [Date]\n⏰ [Time]\n📍 [Place]\n\n🎯 What we'll do:\n• [Activity 1]\n• [Activity 2]\n• [Activity 3]\n\nBring: [What to bring]\n\nJust shoot me a text if you're coming!\n[Your Number]\n\nCan't wait! 🤗"
        ]
      },

      default: [
        "That's interesting! Tell me more about that.",
        "I see! What do you think about that?",
        "Hmm, that's a good point. Can you elaborate?",
        "That's fascinating! I'd love to learn more.",
        "Interesting perspective! What made you think of that?",
        "I'm not sure I fully understand, but I'm listening!",
        "That's something to think about. What's your take on it?",
        "I appreciate you sharing that with me!"
      ]
    };
  }

  async generateResponse(message, username) {
    const lowerMessage = message.toLowerCase().trim();
    
    // Update conversation history
    this.updateConversationHistory(username, message);
    
    // Check for specific patterns
    if (this.isGreeting(lowerMessage)) {
      return this.getRandomResponse(this.responses.greetings);
    }
    
    if (this.isFarewell(lowerMessage)) {
      return this.getRandomResponse(this.responses.farewells);
    }
    
    if (this.isThanking(lowerMessage)) {
      return this.getRandomResponse(this.responses.thanks);
    }
    
    // Handle questions
    if (this.isAskingName(lowerMessage)) {
      return this.getRandomResponse(this.responses.questions.name);
    }
    
    if (this.isAskingTime(lowerMessage)) {
      return this.responses.questions.time();
    }
    
    if (this.isAskingWeather(lowerMessage)) {
      return this.getRandomResponse(this.responses.questions.weather);
    }
    
    if (this.isAskingHelp(lowerMessage)) {
      return this.getRandomResponse(this.responses.questions.help);
    }
    
    // Handle emotions
    if (this.isExpressingEmotion(lowerMessage, 'happy')) {
      return this.getRandomResponse(this.responses.emotions.happy);
    }
    
    if (this.isExpressingEmotion(lowerMessage, 'sad')) {
      return this.getRandomResponse(this.responses.emotions.sad);
    }
    
    if (this.isExpressingEmotion(lowerMessage, 'excited')) {
      return this.getRandomResponse(this.responses.emotions.excited);
    }
    
    // Handle invitation requests
    const invitationType = this.detectInvitationType(lowerMessage);
    if (invitationType) {
      return this.generateInvitation(invitationType, lowerMessage);
    }

    // Handle specific topics
    if (lowerMessage.includes('joke')) {
      return this.getJoke();
    }

    if (lowerMessage.includes('fact')) {
      return this.getFunFact();
    }

    if (lowerMessage.includes('quote')) {
      return this.getInspirationalQuote();
    }
    
    // Personalized responses based on conversation history
    const personalizedResponse = this.getPersonalizedResponse(username, lowerMessage);
    if (personalizedResponse) {
      return personalizedResponse;
    }
    
    // Default response
    return this.getRandomResponse(this.responses.default);
  }

  updateConversationHistory(username, message) {
    if (!this.conversationHistory.has(username)) {
      this.conversationHistory.set(username, []);
    }
    
    const history = this.conversationHistory.get(username);
    history.push({
      message: message,
      timestamp: new Date()
    });
    
    // Keep only last 10 messages
    if (history.length > 10) {
      history.shift();
    }
  }

  getPersonalizedResponse(username, message) {
    const history = this.conversationHistory.get(username);
    if (!history || history.length < 2) return null;
    
    // Check if user is repeating themselves
    const recentMessages = history.slice(-3).map(h => h.message.toLowerCase());
    if (recentMessages.filter(m => m === message).length > 1) {
      return "I notice you mentioned that before. Is there something specific you'd like to explore about it?";
    }
    
    return null;
  }

  isGreeting(message) {
    const greetings = ['hello', 'hi', 'hey', 'greetings', 'good morning', 'good afternoon', 'good evening'];
    return greetings.some(greeting => message.includes(greeting));
  }

  isFarewell(message) {
    const farewells = ['bye', 'goodbye', 'see you', 'farewell', 'take care', 'later'];
    return farewells.some(farewell => message.includes(farewell));
  }

  isThanking(message) {
    const thanks = ['thank', 'thanks', 'appreciate', 'grateful'];
    return thanks.some(thank => message.includes(thank));
  }

  isAskingName(message) {
    return message.includes('your name') || message.includes('who are you') || message.includes('what are you');
  }

  isAskingTime(message) {
    return message.includes('time') || message.includes('date') || message.includes('what time');
  }

  isAskingWeather(message) {
    return message.includes('weather') || message.includes('temperature') || message.includes('rain');
  }

  isAskingHelp(message) {
    return message.includes('help') || message.includes('what can you do') || message.includes('how can you');
  }

  isExpressingEmotion(message, emotion) {
    const emotionWords = {
      happy: ['happy', 'glad', 'joy', 'excited', 'great', 'awesome', 'wonderful', 'fantastic'],
      sad: ['sad', 'down', 'depressed', 'upset', 'unhappy', 'disappointed', 'blue'],
      excited: ['excited', 'thrilled', 'pumped', 'enthusiastic', 'eager', 'can\'t wait']
    };
    
    return emotionWords[emotion]?.some(word => message.includes(word)) || false;
  }

  getJoke() {
    const jokes = [
      "Why don't scientists trust atoms? Because they make up everything!",
      "Why did the scarecrow win an award? He was outstanding in his field!",
      "Why don't eggs tell jokes? They'd crack each other up!",
      "What do you call a fake noodle? An impasta!",
      "Why did the math book look so sad? Because it had too many problems!"
    ];
    return this.getRandomResponse(jokes);
  }

  getFunFact() {
    const facts = [
      "Did you know? Octopuses have three hearts and blue blood!",
      "Fun fact: A group of flamingos is called a 'flamboyance'!",
      "Here's a fact: Honey never spoils. Archaeologists have found edible honey in ancient Egyptian tombs!",
      "Did you know? A single cloud can weigh more than a million pounds!",
      "Fun fact: Bananas are berries, but strawberries aren't!"
    ];
    return this.getRandomResponse(facts);
  }

  getInspirationalQuote() {
    const quotes = [
      "\"The only way to do great work is to love what you do.\" - Steve Jobs",
      "\"Innovation distinguishes between a leader and a follower.\" - Steve Jobs",
      "\"Life is what happens to you while you're busy making other plans.\" - John Lennon",
      "\"The future belongs to those who believe in the beauty of their dreams.\" - Eleanor Roosevelt",
      "\"It is during our darkest moments that we must focus to see the light.\" - Aristotle"
    ];
    return this.getRandomResponse(quotes);
  }

  detectInvitationType(message) {
    // Birthday invitation keywords
    if (message.includes('birthday') && (message.includes('invitation') || message.includes('invite'))) {
      return 'birthday';
    }

    // Wedding invitation keywords
    if ((message.includes('wedding') || message.includes('marriage')) &&
        (message.includes('invitation') || message.includes('invite'))) {
      return 'wedding';
    }

    // Party invitation keywords
    if (message.includes('party') && (message.includes('invitation') || message.includes('invite'))) {
      return 'party';
    }

    // Business/Professional invitation keywords
    if ((message.includes('business') || message.includes('professional') ||
         message.includes('meeting') || message.includes('conference') ||
         message.includes('corporate')) &&
        (message.includes('invitation') || message.includes('invite'))) {
      return 'business';
    }

    // Casual hangout invitation keywords
    if ((message.includes('hangout') || message.includes('get together') ||
         message.includes('casual') || message.includes('friends')) &&
        (message.includes('invitation') || message.includes('invite'))) {
      return 'casual';
    }

    // General invitation request
    if (message.includes('invitation') || message.includes('invite') ||
        message.includes('invitation content') || message.includes('invitation text')) {
      return 'party'; // Default to party invitation
    }

    return null;
  }

  generateInvitation(type, originalMessage) {
    const invitations = this.responses.invitations[type];
    if (!invitations) {
      return "I can help you create invitations! Try asking for: birthday invitation, wedding invitation, party invitation, business invitation, or casual invitation.";
    }

    const template = this.getRandomResponse(invitations);

    // Add helpful instructions
    const instructions = this.getInvitationInstructions(type);

    return `Here's a ${type} invitation template for you:\n\n${template}\n\n${instructions}`;
  }

  getInvitationInstructions(type) {
    const instructions = {
      birthday: "💡 **Instructions:** Replace the placeholders with:\n• [Name] - Birthday person's name\n• [Date] - Party date\n• [Time] - Party time\n• [Address] - Party location\n• [Contact Info] - Your phone/email\n• [RSVP Date] - Response deadline",

      wedding: "💡 **Instructions:** Replace the placeholders with:\n• [Bride's Name] & [Groom's Name] - Couple's names\n• [Wedding Date] - Ceremony date\n• [Time] - Ceremony time\n• [Venue Name] - Wedding venue\n• [Address] - Venue address\n• [RSVP Date] - Response deadline\n• [Contact Information] - RSVP contact",

      party: "💡 **Instructions:** Replace the placeholders with:\n• [Event Type] - Type of party\n• [Date] - Party date\n• [Time] - Party time\n• [Address/Venue] - Party location\n• [Dress Code] - Attire requirements\n• [Contact] - Your contact info",

      business: "💡 **Instructions:** Replace the placeholders with:\n• [Event Name] - Name of the event\n• [Date] - Event date\n• [Time] - Event time\n• [Location] - Event venue\n• [Agenda Items] - Meeting topics\n• [RSVP Date] - Response deadline\n• [Your Name/Organization] - Your details",

      casual: "💡 **Instructions:** Replace the placeholders with:\n• [Name] - Friend's name\n• [Date] - Hangout date\n• [Time] - Hangout time\n• [Location] - Where you'll meet\n• [Activity] - What you'll do\n• [Phone] - Your phone number\n• [What to bring] - Items to bring"
    };

    return instructions[type] || "Replace the placeholders with your specific details!";
  }

  getRandomResponse(responses) {
    return responses[Math.floor(Math.random() * responses.length)];
  }
}

module.exports = ChatBot;
