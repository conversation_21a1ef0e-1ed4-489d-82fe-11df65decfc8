/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --secondary-color: #f3f4f6;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] {
    --primary-color: #6366f1;
    --primary-hover: #5b21b6;
    --secondary-color: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #4b5563;
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    color: var(--text-primary);
    height: 100vh;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Chat Container */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 800px;
    margin: 0 auto;
    background: var(--bg-primary);
    box-shadow: var(--shadow-lg);
}

/* Header */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow);
}

.bot-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.bot-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.bot-details h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.status {
    font-size: 0.8rem;
    opacity: 0.9;
}

.status.online::before {
    content: '●';
    color: var(--success-color);
    margin-right: 0.25rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: var(--bg-secondary);
}

.message {
    display: flex;
    margin-bottom: 1rem;
    animation: slideIn 0.3s ease;
}

.message.user {
    justify-content: flex-end;
}

.message-content {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    position: relative;
    word-wrap: break-word;
}

.message.bot .message-content {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-bottom-left-radius: 0.25rem;
    box-shadow: var(--shadow);
}

.message.user .message-content {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: 0.25rem;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

.welcome-message {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 1rem;
    box-shadow: var(--shadow);
}

.welcome-message .message-content {
    max-width: none;
    padding: 0;
    background: none;
    box-shadow: none;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0 1rem;
    margin-bottom: 1rem;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
    padding: 0.75rem 1rem;
    background: var(--bg-primary);
    border-radius: 1rem;
    box-shadow: var(--shadow);
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: var(--text-secondary);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Input */
.chat-input-container {
    padding: 1rem 1.5rem;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

.chat-input {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

#messageInput {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 1.5rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
    outline: none;
    transition: all 0.2s ease;
}

#messageInput:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.send-btn {
    width: 44px;
    height: 44px;
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Invitation Actions */
.invitation-actions {
    margin-top: 0.75rem;
}

.invitation-actions h4 {
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    text-align: center;
    font-weight: 600;
}

.invitation-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.75rem;
    margin-top: 0.75rem;
}

.invitation-btn {
    padding: 0.8rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border: none;
    border-radius: 1rem;
    color: white;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 6px rgba(79, 70, 229, 0.25);
    text-align: center;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.invitation-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

.invitation-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.modal-content h2 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.modal-content p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

#usernameInput {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    outline: none;
    transition: all 0.2s ease;
}

#usernameInput:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

/* Scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Responsive */
@media (max-width: 768px) {
    .chat-container {
        height: 100vh;
        border-radius: 0;
    }

    .message-content {
        max-width: 85%;
    }

    .input-actions {
        justify-content: center;
    }

    .chat-header {
        padding: 0.75rem 1rem;
    }

    .bot-avatar {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }
}
