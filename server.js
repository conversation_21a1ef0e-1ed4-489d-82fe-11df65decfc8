const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Import chatbot logic
const ChatBot = require('./chatbot');
const bot = new ChatBot();

// Store active users
const activeUsers = new Map();

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('New user connected:', socket.id);
  
  // Handle user joining
  socket.on('user_join', (userData) => {
    activeUsers.set(socket.id, {
      id: socket.id,
      username: userData.username || `User_${socket.id.substring(0, 6)}`,
      joinedAt: new Date()
    });
    
    const user = activeUsers.get(socket.id);
    
    // Send welcome message
    socket.emit('bot_message', {
      message: `Hello ${user.username}! I'm your friendly chatbot. How can I help you today?`,
      timestamp: new Date(),
      type: 'welcome'
    });
    
    // Broadcast user joined to all clients
    socket.broadcast.emit('user_joined', {
      username: user.username,
      timestamp: new Date()
    });
    
    console.log(`User ${user.username} joined the chat`);
  });
  
  // Handle incoming messages
  socket.on('user_message', async (data) => {
    const user = activeUsers.get(socket.id);
    if (!user) return;
    
    const userMessage = {
      username: user.username,
      message: data.message,
      timestamp: new Date(),
      type: 'user'
    };
    
    // Broadcast user message to all clients
    io.emit('user_message', userMessage);
    
    console.log(`${user.username}: ${data.message}`);
    
    // Show typing indicator
    socket.emit('bot_typing', true);
    
    // Get bot response with a slight delay for realism
    setTimeout(async () => {
      try {
        const botResponse = await bot.generateResponse(data.message, user.username);
        
        const botMessage = {
          message: botResponse,
          timestamp: new Date(),
          type: 'bot'
        };
        
        // Send bot response to all clients
        io.emit('bot_message', botMessage);
        
        console.log(`Bot: ${botResponse}`);
      } catch (error) {
        console.error('Error generating bot response:', error);
        socket.emit('bot_message', {
          message: "Sorry, I'm having trouble processing your message right now. Please try again.",
          timestamp: new Date(),
          type: 'error'
        });
      }
    }, Math.random() * 1000 + 500); // Random delay between 0.5-1.5 seconds
  });
  
  // Handle user disconnect
  socket.on('disconnect', () => {
    const user = activeUsers.get(socket.id);
    if (user) {
      console.log(`User ${user.username} disconnected`);
      
      // Broadcast user left to all clients
      socket.broadcast.emit('user_left', {
        username: user.username,
        timestamp: new Date()
      });
      
      activeUsers.delete(socket.id);
    }
  });
  
  // Handle typing indicators
  socket.on('user_typing', (isTyping) => {
    const user = activeUsers.get(socket.id);
    if (user) {
      socket.broadcast.emit('user_typing', {
        username: user.username,
        isTyping: isTyping
      });
    }
  });
});

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date(),
    activeUsers: activeUsers.size
  });
});

app.get('/api/stats', (req, res) => {
  res.json({
    activeUsers: activeUsers.size,
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

// Serve the main page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
server.listen(PORT, () => {
  console.log(`🤖 Chatbot server running on port ${PORT}`);
  console.log(`📱 Open http://localhost:${PORT} to start chatting`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
