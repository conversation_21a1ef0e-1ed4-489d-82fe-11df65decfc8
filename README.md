# 🤖 Node.js Chatbot

A full-featured, real-time chatbot built with Node.js, Express, and Socket.io. Features intelligent responses, modern UI, and real-time communication.

## ✨ Features

- **Real-time messaging** with Socket.io
- **Intelligent responses** with pattern matching and context awareness
- **Modern, responsive UI** with dark/light theme toggle
- **Typing indicators** for realistic chat experience
- **User management** with join/leave notifications
- **Quick action buttons** for common interactions
- **Conversation history** tracking
- **Personalized responses** based on user interaction
- **Built-in jokes, facts, and quotes**
- **Clean, professional design**

## 🚀 Quick Start

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. **Clone or download the project**
   ```bash
   cd chatbot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

## 📁 Project Structure

```
chatbot/
├── server.js          # Main server file with Express and Socket.io
├── chatbot.js         # Chatbot logic and response system
├── package.json       # Project dependencies and scripts
├── .env.example       # Environment variables template
├── README.md          # This file
└── public/
    ├── index.html     # Main chat interface
    ├── style.css      # Styling and themes
    └── script.js      # Frontend JavaScript
```

## 🎯 How It Works

### Backend (Node.js)
- **Express server** serves the web interface and handles HTTP requests
- **Socket.io** manages real-time WebSocket connections
- **ChatBot class** processes messages and generates intelligent responses
- **User management** tracks active users and conversation history

### Frontend (Vanilla JS)
- **ChatApp class** manages the user interface and Socket.io client
- **Real-time messaging** with typing indicators
- **Theme switching** between light and dark modes
- **Responsive design** works on desktop and mobile

### Chatbot Intelligence
- **Pattern matching** for greetings, farewells, questions
- **Emotion detection** responds appropriately to user mood
- **Context awareness** remembers conversation history
- **Built-in content** jokes, facts, quotes, and helpful responses
- **Personalization** adapts responses based on user interaction

## 🎨 Customization

### Adding New Responses

Edit `chatbot.js` to add new response patterns:

```javascript
// Add to the responses object
newCategory: [
    "Response 1",
    "Response 2",
    "Response 3"
]

// Add detection logic in generateResponse method
if (this.isNewPattern(lowerMessage)) {
    return this.getRandomResponse(this.responses.newCategory);
}
```

### Styling

Modify `public/style.css` to customize the appearance:
- Change colors in the CSS variables at the top
- Modify animations and transitions
- Adjust responsive breakpoints

### Adding Features

The modular structure makes it easy to add:
- Database integration for persistent chat history
- User authentication
- Multiple chat rooms
- File sharing
- Voice messages
- Integration with external APIs (OpenAI, etc.)

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```env
PORT=3000
NODE_ENV=development
```

### Scripts

- `npm start` - Run in production mode
- `npm run dev` - Run in development mode with auto-restart
- `npm test` - Run tests (placeholder)

## 🌟 Advanced Features

### Real-time Communication
- Instant message delivery
- Typing indicators
- User join/leave notifications
- Connection status monitoring

### Intelligent Responses
- Context-aware conversations
- Emotion recognition
- Personalized interactions
- Conversation history tracking

### User Experience
- Clean, modern interface
- Dark/light theme toggle
- Quick action buttons
- Responsive design
- Smooth animations

## 🚀 Deployment

### Local Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Docker (Optional)
Create a `Dockerfile`:
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues:

1. Check the console for error messages
2. Ensure all dependencies are installed
3. Verify Node.js version compatibility
4. Check if port 3000 is available

## 🎉 Enjoy Your Chatbot!

Your chatbot is now ready to use! Open `http://localhost:3000` in your browser and start chatting.

The bot can:
- Have casual conversations
- Tell jokes and share fun facts
- Provide inspirational quotes
- Remember your conversation context
- Respond to your emotions
- And much more!

Happy chatting! 🤖💬
