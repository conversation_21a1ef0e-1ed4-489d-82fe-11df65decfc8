<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBot - AI Assistant</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="chat-header">
            <div class="bot-info">
                <div class="bot-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="bot-details">
                    <h3>ChatBot</h3>
                    <span class="status online">Online</span>
                </div>
            </div>
            <div class="header-actions">
                <button id="clearChat" class="btn-icon" title="Clear Chat">
                    <i class="fas fa-trash"></i>
                </button>
                <button id="toggleTheme" class="btn-icon" title="Toggle Theme">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <div class="bot-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>Welcome to ChatBot! I'm here to help and chat with you. What would you like to talk about?</p>
                </div>
            </div>
        </div>

        <!-- Typing Indicator -->
        <div class="typing-indicator" id="typingIndicator" style="display: none;">
            <div class="bot-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="chat-input-container">
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="Type your message here..." autocomplete="off">
                <button id="sendButton" class="send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-actions">
                <button class="quick-action" data-message="Hello!">👋 Hello</button>
                <button class="quick-action" data-message="Tell me a joke">😄 Joke</button>
                <button class="quick-action" data-message="What's a fun fact?">🧠 Fun Fact</button>
                <button class="quick-action" data-message="Give me a quote">💭 Quote</button>
            </div>
            <div class="invitation-actions">
                <h4>🎉 Need an Invitation?</h4>
                <div class="invitation-buttons">
                    <button class="invitation-btn" data-message="Create a birthday invitation">🎂 Birthday</button>
                    <button class="invitation-btn" data-message="Create a wedding invitation">💍 Wedding</button>
                    <button class="invitation-btn" data-message="Create a party invitation">🎊 Party</button>
                    <button class="invitation-btn" data-message="Create a business invitation">💼 Business</button>
                    <button class="invitation-btn" data-message="Create a casual invitation">😊 Casual</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Username Modal -->
    <div class="modal" id="usernameModal">
        <div class="modal-content">
            <h2>Welcome to ChatBot!</h2>
            <p>Please enter your name to start chatting:</p>
            <input type="text" id="usernameInput" placeholder="Your name" maxlength="20">
            <button id="joinChat" class="btn-primary">Start Chatting</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
