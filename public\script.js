class ChatApp {
    constructor() {
        this.socket = null;
        this.username = '';
        this.isTyping = false;
        this.typingTimeout = null;
        this.theme = localStorage.getItem('chatbot-theme') || 'light';
        
        this.initializeElements();
        this.initializeEventListeners();
        this.applyTheme();
        this.showUsernameModal();
    }

    initializeElements() {
        this.elements = {
            usernameModal: document.getElementById('usernameModal'),
            usernameInput: document.getElementById('usernameInput'),
            joinChatBtn: document.getElementById('joinChat'),
            chatMessages: document.getElementById('chatMessages'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendButton'),
            typingIndicator: document.getElementById('typingIndicator'),
            clearChatBtn: document.getElementById('clearChat'),
            toggleThemeBtn: document.getElementById('toggleTheme'),
            quickActions: document.querySelectorAll('.quick-action')
        };
    }

    initializeEventListeners() {
        // Username modal
        this.elements.joinChatBtn.addEventListener('click', () => this.joinChat());
        this.elements.usernameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.joinChat();
        });

        // Message input
        this.elements.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        this.elements.messageInput.addEventListener('input', () => {
            this.handleTyping();
        });

        this.elements.sendButton.addEventListener('click', () => this.sendMessage());

        // Header actions
        this.elements.clearChatBtn.addEventListener('click', () => this.clearChat());
        this.elements.toggleThemeBtn.addEventListener('click', () => this.toggleTheme());

        // Quick actions
        this.elements.quickActions.forEach(btn => {
            btn.addEventListener('click', () => {
                const message = btn.getAttribute('data-message');
                this.elements.messageInput.value = message;
                this.sendMessage();
            });
        });
    }

    showUsernameModal() {
        this.elements.usernameModal.style.display = 'flex';
        this.elements.usernameInput.focus();
    }

    joinChat() {
        const username = this.elements.usernameInput.value.trim();
        if (!username) {
            this.showNotification('Please enter a valid username', 'error');
            return;
        }

        this.username = username;
        this.elements.usernameModal.style.display = 'none';
        this.initializeSocket();
    }

    initializeSocket() {
        this.socket = io();

        // Connection events
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.socket.emit('user_join', { username: this.username });
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.showNotification('Connection lost. Trying to reconnect...', 'warning');
        });

        // Message events
        this.socket.on('bot_message', (data) => {
            this.hideTypingIndicator();
            this.addMessage(data.message, 'bot', data.timestamp);
        });

        this.socket.on('user_message', (data) => {
            if (data.username !== this.username) {
                this.addMessage(data.message, 'user', data.timestamp, data.username);
            }
        });

        // Typing events
        this.socket.on('bot_typing', (isTyping) => {
            if (isTyping) {
                this.showTypingIndicator();
            } else {
                this.hideTypingIndicator();
            }
        });

        this.socket.on('user_typing', (data) => {
            // Handle other users typing (if implementing multi-user chat)
            console.log(`${data.username} is typing: ${data.isTyping}`);
        });

        // User events
        this.socket.on('user_joined', (data) => {
            this.addSystemMessage(`${data.username} joined the chat`, data.timestamp);
        });

        this.socket.on('user_left', (data) => {
            this.addSystemMessage(`${data.username} left the chat`, data.timestamp);
        });
    }

    sendMessage() {
        const message = this.elements.messageInput.value.trim();
        if (!message || !this.socket) return;

        // Add user message to chat
        this.addMessage(message, 'user', new Date(), this.username);

        // Send to server
        this.socket.emit('user_message', { message });

        // Clear input
        this.elements.messageInput.value = '';
        this.stopTyping();
    }

    addMessage(content, type, timestamp, username = null) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        if (type === 'bot') {
            const avatar = document.createElement('div');
            avatar.className = 'bot-avatar';
            avatar.innerHTML = '<i class="fas fa-robot"></i>';
            messageDiv.appendChild(avatar);
        }

        const messageText = document.createElement('p');
        messageText.textContent = content;
        messageContent.appendChild(messageText);

        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = this.formatTime(timestamp);
        messageContent.appendChild(messageTime);

        messageDiv.appendChild(messageContent);
        this.elements.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    addSystemMessage(content, timestamp) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'system-message';
        messageDiv.style.cssText = `
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin: 0.5rem 0;
            opacity: 0.7;
        `;
        messageDiv.textContent = `${content} at ${this.formatTime(timestamp)}`;
        this.elements.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        this.elements.typingIndicator.style.display = 'flex';
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        this.elements.typingIndicator.style.display = 'none';
    }

    handleTyping() {
        if (!this.isTyping && this.socket) {
            this.isTyping = true;
            this.socket.emit('user_typing', true);
        }

        clearTimeout(this.typingTimeout);
        this.typingTimeout = setTimeout(() => {
            this.stopTyping();
        }, 1000);
    }

    stopTyping() {
        if (this.isTyping && this.socket) {
            this.isTyping = false;
            this.socket.emit('user_typing', false);
        }
        clearTimeout(this.typingTimeout);
    }

    clearChat() {
        if (confirm('Are you sure you want to clear the chat history?')) {
            this.elements.chatMessages.innerHTML = `
                <div class="welcome-message">
                    <div class="bot-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>Chat cleared! How can I help you today?</p>
                    </div>
                </div>
            `;
        }
    }

    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('chatbot-theme', this.theme);
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        const themeIcon = this.elements.toggleThemeBtn.querySelector('i');
        themeIcon.className = this.theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem;
            background: var(--${type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'success'}-color);
            color: white;
            border-radius: 0.5rem;
            box-shadow: var(--shadow-lg);
            z-index: 1001;
            animation: slideIn 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    scrollToBottom() {
        setTimeout(() => {
            this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
        }, 100);
    }
}

// Initialize the chat app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});
