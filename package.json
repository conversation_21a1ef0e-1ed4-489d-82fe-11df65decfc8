{"name": "chatbot", "version": "1.0.0", "description": "A full-featured Node.js chatbot with real-time messaging and intelligent responses", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["chatbot", "nodejs", "socket.io", "express", "ai", "chat"], "author": "Your Name", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}